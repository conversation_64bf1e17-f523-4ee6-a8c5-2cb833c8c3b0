import express from 'express';
import { TestController } from '../controllers/TestController.js';
import { validate } from '../middleware/validation.js';
import <PERSON><PERSON> from 'joi';

const router = express.Router();

// Validation schemas
const emailTestSchema = Joi.object({
  email: Joi.string().email().required(),
});

/**
 * @route POST /api/test/email
 * @desc Test email configuration
 * @access Public (for development/testing only)
 */
router.post('/email', validate(emailTestSchema), TestController.testEmail);

/**
 * @route POST /api/test/otp-email
 * @desc Test OTP email sending
 * @access Public (for development/testing only)
 */
router.post('/otp-email', validate(emailTestSchema), TestController.testOTPEmail);

/**
 * @route GET /api/test/email-status
 * @desc Get email service status
 * @access Public (for development/testing only)
 */
router.get('/email-status', TestController.getEmailStatus);

export default router;
