import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import logger from '../config/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Email Service Class
 * Handles all email sending functionality with beautiful templates
 */
export class EmailService {
  static transporter = null;
  static templates = new Map();

  /**
   * Initialize email service with SMTP configuration
   */
  static async initialize() {
    try {
      // Check if SMTP credentials are configured
      if (!process.env.SMTP_PASSWORD) {
        logger.warn('SMTP password not configured. Email service will be initialized in test mode.');
        this.transporter = null;
        await this.loadTemplates();
        logger.info('Email service initialized in test mode (no SMTP credentials)');
        return;
      }

      // Create SMTP transporter
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtpout.secureserver.net',
        port: parseInt(process.env.SMTP_PORT || '465'),
        secure: process.env.SMTP_SECURE === 'true' || true, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASSWORD,
        },
        tls: {
          // Do not fail on invalid certs for development
          rejectUnauthorized: process.env.NODE_ENV === 'production',
        },
      });

      // Verify SMTP connection
      if (process.env.NODE_ENV !== 'test') {
        await this.verifyConnection();
      }

      // Load email templates
      await this.loadTemplates();

      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Error initializing email service:', error);
      // Don't throw error in development to allow server to start
      if (process.env.NODE_ENV === 'production') {
        throw error;
      } else {
        logger.warn('Email service initialization failed, continuing in test mode');
        this.transporter = null;
        await this.loadTemplates();
      }
    }
  }

  /**
   * Verify SMTP connection
   */
  static async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('SMTP connection verified successfully');
    } catch (error) {
      logger.error('SMTP connection verification failed:', error);
      throw new Error('Failed to connect to SMTP server. Please check your email configuration.');
    }
  }

  /**
   * Load email templates from the templates directory
   */
  static async loadTemplates() {
    try {
      const templatesDir = path.join(__dirname, '../templates/email');
      
      // Load base template
      const baseTemplatePath = path.join(templatesDir, 'base.hbs');
      const baseTemplateContent = await fs.readFile(baseTemplatePath, 'utf-8');
      this.templates.set('base', handlebars.compile(baseTemplateContent));

      // Load OTP template
      const otpTemplatePath = path.join(templatesDir, 'otp-verification.hbs');
      const otpTemplateContent = await fs.readFile(otpTemplatePath, 'utf-8');
      this.templates.set('otp-verification', handlebars.compile(otpTemplateContent));

      logger.info('Email templates loaded successfully');
    } catch (error) {
      logger.error('Error loading email templates:', error);
      throw error;
    }
  }

  /**
   * Send OTP verification email
   * @param {string} email - Recipient email address
   * @param {string} otp - OTP code
   * @param {string} action - Action type (signup, login, etc.)
   * @param {number} expiryMinutes - OTP expiry time in minutes
   * @returns {Promise<boolean>} Success status
   */
  static async sendOTPEmail(email, otp, action = 'verify', expiryMinutes = 5) {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not configured. Would send OTP ${otp} to ${email} for ${action}`);
        return true; // Return true in development to allow testing
      }

      // Prepare template data
      const templateData = {
        otp,
        action,
        expiryMinutes,
      };

      // Generate email content
      const otpTemplate = this.templates.get('otp-verification');
      const baseTemplate = this.templates.get('base');
      
      if (!otpTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const otpContent = otpTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Verify Your Account - TheInfini AI',
        body: otpContent,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'TheInfini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `🔐 Your verification code for TheInfini AI`,
        html: fullEmailContent,
        text: this.generatePlainTextOTP(otp, action, expiryMinutes),
      };

      // Send email
      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info(`OTP email sent successfully to ${email}`, {
        messageId: info.messageId,
        action,
      });

      return true;
    } catch (error) {
      logger.error('Error sending OTP email:', error);
      return false;
    }
  }

  /**
   * Generate plain text version of OTP email
   * @param {string} otp - OTP code
   * @param {string} action - Action type
   * @param {number} expiryMinutes - Expiry time in minutes
   * @returns {string} Plain text email content
   */
  static generatePlainTextOTP(otp, action, expiryMinutes) {
    return `
Hello!

We received a request to verify your account with TheInfini AI.

Your verification code is: ${otp}

This code expires in ${expiryMinutes} minutes.

Please enter this code to ${action} your account. If you didn't request this verification, please ignore this email.

For security reasons, never share this code with anyone.

Need help? Contact our support <NAME_EMAIL>

Best regards,
TheInfini AI Team
    `.trim();
  }

  /**
   * Send welcome email (for future use)
   * @param {string} email - Recipient email address
   * @param {string} name - User name
   * @returns {Promise<boolean>} Success status
   */
  static async sendWelcomeEmail(email, name = 'there') {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not configured. Would send welcome email to ${email}`);
        return true; // Return true in development to allow testing
      }

      const mailOptions = {
        from: {
          name: 'TheInfini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: '🎉 Welcome to TheInfini AI!',
        html: `
          <h2>Welcome to TheInfini AI, ${name}!</h2>
          <p>Thank you for joining our platform. We're excited to have you on board!</p>
          <p>You can now start exploring our AI-powered features and capabilities.</p>
          <p>If you have any questions, feel free to reach out to our support team.</p>
          <p>Best regards,<br>TheInfini AI Team</p>
        `,
        text: `Welcome to TheInfini AI, ${name}! Thank you for joining our platform.`,
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Welcome email sent successfully to ${email}`, {
        messageId: info.messageId,
      });

      return true;
    } catch (error) {
      logger.error('Error sending welcome email:', error);
      return false;
    }
  }

  /**
   * Test email configuration
   * @param {string} testEmail - Email to send test message to
   * @returns {Promise<boolean>} Success status
   */
  static async testEmailConfiguration(testEmail) {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not configured. Would send test email to ${testEmail}`);
        return true; // Return true in development to allow testing
      }

      const mailOptions = {
        from: {
          name: 'TheInfini AI',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: testEmail,
        subject: '✅ Email Configuration Test - TheInfini AI',
        html: `
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify that your SMTP configuration is working correctly.</p>
          <p>If you received this email, your email service is configured properly!</p>
          <p>Timestamp: ${new Date().toISOString()}</p>
        `,
        text: 'Email configuration test successful!',
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Test email sent successfully to ${testEmail}`, {
        messageId: info.messageId,
      });

      return true;
    } catch (error) {
      logger.error('Error sending test email:', error);
      return false;
    }
  }

  /**
   * Get email service status
   * @returns {Object} Service status information
   */
  static getStatus() {
    return {
      initialized: !!this.transporter,
      templatesLoaded: this.templates.size,
      smtpHost: process.env.SMTP_HOST || 'smtpout.secureserver.net',
      smtpPort: process.env.SMTP_PORT || '465',
      fromEmail: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
    };
  }
}
