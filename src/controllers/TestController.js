import { EmailService } from '../services/EmailService.js';
import { ResponseUtil } from '../utils/response.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';

/**
 * Test Controller
 * Handles testing endpoints for various services
 */
export class TestController {
  /**
   * Test email configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static testEmail = asyncHandler(async (req, res) => {
    const { email } = req.body;

    if (!email) {
      return ResponseUtil.error(res, 'Email address is required', 400);
    }

    try {
      const result = await EmailService.testEmailConfiguration(email);
      
      if (result) {
        ResponseUtil.success(res, 'Test email sent successfully', {
          email,
          timestamp: new Date().toISOString(),
        });
      } else {
        ResponseUtil.error(res, 'Failed to send test email', 500);
      }
    } catch (error) {
      logger.error('Error in test email:', error);
      ResponseUtil.error(res, 'Failed to send test email', 500);
    }
  });

  /**
   * Test OTP email
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static testOTPEmail = asyncHandler(async (req, res) => {
    const { email } = req.body;

    if (!email) {
      return ResponseUtil.error(res, 'Email address is required', 400);
    }

    try {
      const testOTP = '123456';
      const result = await EmailService.sendOTPEmail(email, testOTP, 'test', 5);
      
      if (result) {
        ResponseUtil.success(res, 'Test OTP email sent successfully', {
          email,
          otp: testOTP,
          timestamp: new Date().toISOString(),
        });
      } else {
        ResponseUtil.error(res, 'Failed to send test OTP email', 500);
      }
    } catch (error) {
      logger.error('Error in test OTP email:', error);
      ResponseUtil.error(res, 'Failed to send test OTP email', 500);
    }
  });

  /**
   * Get email service status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getEmailStatus = asyncHandler(async (req, res) => {
    try {
      const status = EmailService.getStatus();
      ResponseUtil.success(res, 'Email service status retrieved', status);
    } catch (error) {
      logger.error('Error getting email status:', error);
      ResponseUtil.error(res, 'Failed to get email service status', 500);
    }
  });
}
