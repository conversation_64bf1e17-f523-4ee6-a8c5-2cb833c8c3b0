# Email Service Setup Guide

## Overview

This guide explains how to set up the email service for TheInfini AI backend, specifically configured for GoDaddy SMTP servers.

## GoDaddy SMTP Configuration

### SMTP Settings

Based on your GoDaddy hosting, use the following SMTP configuration:

```
Host: smtpout.secureserver.net
Port: 465 (SSL)
Security: SSL/TLS
Authentication: Required
```

### Environment Variables

Add the following variables to your `.env` file:

```env
# Email/SMTP Configuration (GoDaddy)
SMTP_HOST=smtpout.secureserver.net
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_FROM_EMAIL=<EMAIL>
```

### Setting Up Email Account in GoDaddy

1. **Log into your GoDaddy account**
2. **Go to Email & Office Dashboard**
3. **Create or configure the email account**: `<EMAIL>`
4. **Set a strong password** for the email account
5. **Enable SMTP access** (usually enabled by default)

### Security Considerations

- **Use App Passwords**: If you have 2FA enabled, create an app-specific password
- **Strong Password**: Use a strong, unique password for the email account
- **Environment Security**: Never commit your `.env` file with real credentials
- **IP Restrictions**: Consider setting up IP restrictions in GoDaddy if available

## Email Templates

The email service uses Handlebars templates located in `src/templates/email/`:

- `base.hbs` - Base template with TheInfini AI branding
- `otp-verification.hbs` - OTP verification email template

### Template Features

- **Responsive Design**: Works on desktop and mobile devices
- **Beautiful Styling**: Modern gradient design with TheInfini AI branding
- **Security Notices**: Built-in security warnings for users
- **Accessibility**: Proper contrast and readable fonts

## Testing Email Configuration

### 1. Check Email Service Status

```bash
GET /api/test/email-status
```

Response:
```json
{
  "success": true,
  "message": "Email service status retrieved",
  "data": {
    "initialized": true,
    "templatesLoaded": 2,
    "smtpHost": "smtpout.secureserver.net",
    "smtpPort": "465",
    "fromEmail": "<EMAIL>"
  }
}
```

### 2. Send Test Email

```bash
POST /api/test/email
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### 3. Send Test OTP Email

```bash
POST /api/test/otp-email
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify email credentials in GoDaddy
   - Check if 2FA requires app password
   - Ensure SMTP is enabled for the account

2. **Connection Timeout**
   - Verify SMTP host and port settings
   - Check firewall settings
   - Ensure SSL/TLS is properly configured

3. **Email Not Delivered**
   - Check spam/junk folders
   - Verify sender reputation
   - Check GoDaddy email logs

### Debug Mode

In development mode, OTP codes are logged to the console for testing:

```
[INFO] <NAME_EMAIL>: 123456
```

### Email Service Logs

Monitor the application logs for email-related messages:

```bash
# Success
[INFO] Email service initialized successfully
[INFO] OTP email sent <NAME_EMAIL>

# Errors
[ERROR] SMTP connection verification failed
[ERROR] Error sending OTP email: [error details]
```

## Production Considerations

### 1. Rate Limiting

Implement rate limiting for OTP requests to prevent abuse:

- Maximum 3 OTP requests per email per hour
- Exponential backoff for failed attempts

### 2. Email Deliverability

- **SPF Records**: Configure SPF records for your domain
- **DKIM**: Set up DKIM signing if supported by GoDaddy
- **DMARC**: Implement DMARC policy for email authentication

### 3. Monitoring

- Monitor email delivery rates
- Set up alerts for SMTP failures
- Track bounce rates and spam complaints

### 4. Backup SMTP

Consider setting up a backup SMTP service (like SendGrid or AWS SES) for high availability.

## Email Types Supported

### 1. OTP Verification Emails

- **Signup OTP**: Sent during user registration
- **Login OTP**: Sent for passwordless login
- **Action-specific**: Different messaging based on context

### 2. Welcome Emails (Future)

- Sent after successful account verification
- Includes getting started information

### 3. Notification Emails (Future)

- Account security alerts
- Feature announcements
- System maintenance notifications

## API Integration

The email service is automatically integrated with the authentication flow:

```javascript
// Signup process
const user = await User.createUser(userData);
const otp = await OTPService.generateOTP(user.id);
await OTPService.sendOTP(email, otp, 'signup');

// Login process
const otp = await OTPService.generateOTP(user.id);
await OTPService.sendOTP(email, otp, 'login');
```

## Support

For email-related issues:

1. Check the application logs
2. Verify GoDaddy email account settings
3. Test with the provided test endpoints
4. Contact GoDaddy support for SMTP-specific issues
